import styled from '@emotion/styled';
import { Checkmark } from '@ui/display/checkmark/components/Checkmark';
import { FontFamily } from '@ui/input/types/FontFamily';
import {
  AnimatePresence,
  motion,
  useAnimation
} from 'framer-motion';
import React from 'react';

const StyledFontFamilyBackground = styled.div`
  align-items: center;
  background: ${({ theme }) => theme.grayScale.gray15};
  border: 1px solid ${({ theme }) => theme.grayScale.gray20};
  border-radius: ${({ theme }) => theme.border.radius.md};
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  height: 80px;
  justify-content: center;
  overflow: hidden;
  width: 160px;
  position: relative;
`;

const StyledFontFamilyContent = styled(motion.div)<{
  fontFamily: FontFamily;
}>`
  color: ${({ theme }) => theme.grayScale.gray60};
  display: flex;
  font-size: 18px;
  font-family: ${({ fontFamily }) => {
    switch (fontFamily) {
      case 'Inter':
        return 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif';
      case 'Roboto':
        return 'Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif';
      case 'Open Sans':
        return '"Open Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif';
      case 'Lato':
        return 'Lato, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif';
      case 'Poppins':
        return 'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif';
      case 'System':
      default:
        return '-apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif';
    }
  }};
  font-weight: 400;
  text-align: center;
`;

const StyledContainer = styled.div`
  position: relative;
  width: 160px;
`;

const StyledCheckmarkContainer = styled(motion.div)`
  bottom: 0px;
  padding: ${({ theme }) => theme.spacing(2)};
  position: absolute;
  right: 0px;
`;

export type FontFamilyCardProps = {
  variant: FontFamily;
  selected?: boolean;
} & React.ComponentPropsWithoutRef<'div'>;

const checkmarkAnimationVariants = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 },
};

export const FontFamilyCard = ({
  variant,
  selected,
  onClick,
}: FontFamilyCardProps) => {
  const controls = useAnimation();

  const handleMouseEnter = () => {
    controls.start({
      fontSize: '20px',
      transition: { duration: 0.1 },
    });
  };

  const handleMouseLeave = () => {
    controls.start({
      fontSize: '18px',
      transition: { duration: 0.1 },
    });
  };

  return (
    <StyledContainer>
      <StyledFontFamilyBackground
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={onClick}
      >
        <StyledFontFamilyContent animate={controls} fontFamily={variant}>
          Sample Aa
        </StyledFontFamilyContent>
      </StyledFontFamilyBackground>
      <AnimatePresence>
        {selected && (
          <StyledCheckmarkContainer
            key={variant}
            variants={checkmarkAnimationVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{ duration: 0.3 }}
          >
            <Checkmark />
          </StyledCheckmarkContainer>
        )}
      </AnimatePresence>
    </StyledContainer>
  );
};