import { renderHook, act } from '@testing-library/react';
import { RecoilRoot } from 'recoil';
import { ReactNode } from 'react';

import { useFontFamily } from '../useFontFamily';
import { currentWorkspaceMemberState } from '@/auth/states/currentWorkspaceMemberState';
import { useUpdateOneRecord } from '@/object-record/hooks/useUpdateOneRecord';
import { FontFamily } from '@/workspace-member/types/WorkspaceMember';

// Mock the useUpdateOneRecord hook
jest.mock('@/object-record/hooks/useUpdateOneRecord');
const mockUseUpdateOneRecord = useUpdateOneRecord as jest.MockedFunction<typeof useUpdateOneRecord>;

// Mock console.warn to test error handling
const mockConsoleWarn = jest.spyOn(console, 'warn').mockImplementation(() => {});

const mockUpdateOneWorkspaceMember = jest.fn();

const wrapper = ({ children }: { children: ReactNode }) => (
  <RecoilRoot>{children}</RecoilRoot>
);

const mockWorkspaceMember = {
  id: 'test-id',
  name: { firstName: 'Test', lastName: 'User' },
  userEmail: '<EMAIL>',
  userId: 'user-id',
  colorScheme: 'Light' as const,
  createdAt: '2023-01-01',
  updatedAt: '2023-01-01',
  locale: 'en',
  fontFamily: 'System' as FontFamily,
};

describe('useFontFamily', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseUpdateOneRecord.mockReturnValue({
      updateOneRecord: mockUpdateOneWorkspaceMember,
    });
  });

  afterEach(() => {
    mockConsoleWarn.mockClear();
  });

  afterAll(() => {
    mockConsoleWarn.mockRestore();
  });

  describe('fontFamily getter', () => {
    it('should return "System" as default when no workspace member', () => {
      const { result } = renderHook(() => useFontFamily(), { wrapper });
      expect(result.current.fontFamily).toBe('System');
    });

    it('should return frontend value when fontFamily is already a frontend value', () => {
      const { result } = renderHook(
        () => useFontFamily(),
        {
          wrapper: ({ children }) => (
            <RecoilRoot
              initializeState={(snapshot) => {
                snapshot.set(currentWorkspaceMemberState, {
                  ...mockWorkspaceMember,
                  fontFamily: 'Inter',
                });
              }}
            >
              {children}
            </RecoilRoot>
          ),
        }
      );
      expect(result.current.fontFamily).toBe('Inter');
    });

    it('should convert backend enum to frontend value', () => {
      const { result } = renderHook(
        () => useFontFamily(),
        {
          wrapper: ({ children }) => (
            <RecoilRoot
              initializeState={(snapshot) => {
                snapshot.set(currentWorkspaceMemberState, {
                  ...mockWorkspaceMember,
                  fontFamily: 'OPEN_SANS' as any,
                });
              }}
            >
              {children}
            </RecoilRoot>
          ),
        }
      );
      expect(result.current.fontFamily).toBe('Open Sans');
    });

    it('should handle invalid backend enum gracefully', () => {
      const { result } = renderHook(
        () => useFontFamily(),
        {
          wrapper: ({ children }) => (
            <RecoilRoot
              initializeState={(snapshot) => {
                snapshot.set(currentWorkspaceMemberState, {
                  ...mockWorkspaceMember,
                  fontFamily: 'INVALID_ENUM' as any,
                });
              }}
            >
              {children}
            </RecoilRoot>
          ),
        }
      );
      expect(result.current.fontFamily).toBe('INVALID_ENUM');
      expect(mockConsoleWarn).not.toHaveBeenCalled(); // Should not warn for unknown frontend values
    });

    it('should return "System" when fontFamily is null', () => {
      const { result } = renderHook(
        () => useFontFamily(),
        {
          wrapper: ({ children }) => (
            <RecoilRoot
              initializeState={(snapshot) => {
                snapshot.set(currentWorkspaceMemberState, {
                  ...mockWorkspaceMember,
                  fontFamily: null,
                });
              }}
            >
              {children}
            </RecoilRoot>
          ),
        }
      );
      expect(result.current.fontFamily).toBe('System');
    });
  });

  describe('setFontFamily', () => {
    it('should update state and call API with backend enum value', async () => {
      const { result } = renderHook(
        () => useFontFamily(),
        {
          wrapper: ({ children }) => (
            <RecoilRoot
              initializeState={(snapshot) => {
                snapshot.set(currentWorkspaceMemberState, mockWorkspaceMember);
              }}
            >
              {children}
            </RecoilRoot>
          ),
        }
      );

      await act(async () => {
        await result.current.setFontFamily('Open Sans');
      });

      expect(mockUpdateOneWorkspaceMember).toHaveBeenCalledWith({
        idToUpdate: 'test-id',
        updateOneRecordInput: {
          fontFamily: 'OPEN_SANS',
        },
      });
    });

    it('should not call API when no workspace member', async () => {
      const { result } = renderHook(() => useFontFamily(), { wrapper });

      await act(async () => {
        await result.current.setFontFamily('Inter');
      });

      expect(mockUpdateOneWorkspaceMember).not.toHaveBeenCalled();
    });

    it('should handle invalid font family gracefully', async () => {
      const { result } = renderHook(
        () => useFontFamily(),
        {
          wrapper: ({ children }) => (
            <RecoilRoot
              initializeState={(snapshot) => {
                snapshot.set(currentWorkspaceMemberState, mockWorkspaceMember);
              }}
            >
              {children}
            </RecoilRoot>
          ),
        }
      );

      await act(async () => {
        await result.current.setFontFamily('InvalidFont' as FontFamily);
      });

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        'Unknown font family: InvalidFont, falling back to SYSTEM'
      );
      expect(mockUpdateOneWorkspaceMember).toHaveBeenCalledWith({
        idToUpdate: 'test-id',
        updateOneRecordInput: {
          fontFamily: 'SYSTEM',
        },
      });
    });

    it('should update frontend state with frontend value', async () => {
      let currentState: any;
      const { result } = renderHook(
        () => useFontFamily(),
        {
          wrapper: ({ children }) => (
            <RecoilRoot
              initializeState={(snapshot) => {
                snapshot.set(currentWorkspaceMemberState, mockWorkspaceMember);
              }}
            >
              {children}
            </RecoilRoot>
          ),
        }
      );

      await act(async () => {
        await result.current.setFontFamily('Roboto');
      });

      expect(result.current.fontFamily).toBe('Roboto');
    });
  });

  describe('fontFamilyList', () => {
    it('should return correct font family options', () => {
      const { result } = renderHook(() => useFontFamily(), { wrapper });
      
      const expectedList = [
        { id: 'System', label: 'System' },
        { id: 'Inter', label: 'Inter' },
        { id: 'Roboto', label: 'Roboto' },
        { id: 'Open Sans', label: 'Open Sans' },
        { id: 'Lato', label: 'Lato' },
        { id: 'Poppins', label: 'Poppins' },
      ];

      expect(result.current.fontFamilyList).toEqual(expectedList);
    });
  });

  describe('conversion functions edge cases', () => {
    it('should handle all valid backend enum values', () => {
      const backendValues = ['SYSTEM', 'INTER', 'ROBOTO', 'OPEN_SANS', 'LATO', 'POPPINS'];
      const expectedFrontendValues = ['System', 'Inter', 'Roboto', 'Open Sans', 'Lato', 'Poppins'];

      backendValues.forEach((backendValue, index) => {
        const { result } = renderHook(
          () => useFontFamily(),
          {
            wrapper: ({ children }) => (
              <RecoilRoot
                initializeState={(snapshot) => {
                  snapshot.set(currentWorkspaceMemberState, {
                    ...mockWorkspaceMember,
                    fontFamily: backendValue as any,
                  });
                }}
              >
                {children}
              </RecoilRoot>
            ),
          }
        );
        expect(result.current.fontFamily).toBe(expectedFrontendValues[index]);
      });
    });

    it('should handle all valid frontend values when setting', async () => {
      const frontendValues: FontFamily[] = ['System', 'Inter', 'Roboto', 'Open Sans', 'Lato', 'Poppins'];
      const expectedBackendValues = ['SYSTEM', 'INTER', 'ROBOTO', 'OPEN_SANS', 'LATO', 'POPPINS'];

      for (let i = 0; i < frontendValues.length; i++) {
        const { result } = renderHook(
          () => useFontFamily(),
          {
            wrapper: ({ children }) => (
              <RecoilRoot
                initializeState={(snapshot) => {
                  snapshot.set(currentWorkspaceMemberState, mockWorkspaceMember);
                }}
              >
                {children}
              </RecoilRoot>
            ),
          }
        );

        await act(async () => {
          await result.current.setFontFamily(frontendValues[i]);
        });

        expect(mockUpdateOneWorkspaceMember).toHaveBeenCalledWith({
          idToUpdate: 'test-id',
          updateOneRecordInput: {
            fontFamily: expectedBackendValues[i],
          },
        });

        mockUpdateOneWorkspaceMember.mockClear();
      }
    });
  });
});