import { useCallback } from 'react';
import { useRecoilState } from 'recoil';

import { currentWorkspaceMemberState } from '@/auth/states/currentWorkspaceMemberState';
import { CoreObjectNameSingular } from '@/object-metadata/types/CoreObjectNameSingular';
import { useUpdateOneRecord } from '@/object-record/hooks/useUpdateOneRecord';
import { FontFamily } from '@/workspace-member/types/WorkspaceMember';

// Define backend enum values as const for better type safety
const BACKEND_FONT_FAMILY_ENUM = {
  SYSTEM: 'SYSTEM',
  INTER: 'INTER',
  ROBOTO: 'ROBOTO',
  OPEN_SANS: 'OPEN_SANS',
  LATO: 'LATO',
  POPPINS: 'POPPINS',
} as const;

type BackendFontFamilyEnum = typeof BACKEND_FONT_FAMILY_ENUM[keyof typeof BACKEND_FONT_FAMILY_ENUM];

// Conversion function to map frontend values to backend enum values
const fontFamilyToBackendEnum = (fontFamily: FontFamily): string => {
  const mapping: Record<FontFamily, string> = {
    'System': BACKEND_FONT_FAMILY_ENUM.SYSTEM,
    'Inter': BACKEND_FONT_FAMILY_ENUM.INTER,
    'Roboto': BACKEND_FONT_FAMILY_ENUM.ROBOTO,
    'Open Sans': BACKEND_FONT_FAMILY_ENUM.OPEN_SANS,
    'Lato': BACKEND_FONT_FAMILY_ENUM.LATO,
    'Poppins': BACKEND_FONT_FAMILY_ENUM.POPPINS,
  };
  const result = mapping[fontFamily];
  if (!result) {
    console.warn(`Unknown font family: ${fontFamily}, falling back to SYSTEM`);
    return BACKEND_FONT_FAMILY_ENUM.SYSTEM;
  }
  return result;
};

// Conversion function to map backend enum values to frontend values
const backendEnumToFontFamily = (backendValue: string): FontFamily => {
  const mapping: Record<string, FontFamily> = {
    [BACKEND_FONT_FAMILY_ENUM.SYSTEM]: 'System',
    [BACKEND_FONT_FAMILY_ENUM.INTER]: 'Inter',
    [BACKEND_FONT_FAMILY_ENUM.ROBOTO]: 'Roboto',
    [BACKEND_FONT_FAMILY_ENUM.OPEN_SANS]: 'Open Sans',
    [BACKEND_FONT_FAMILY_ENUM.LATO]: 'Lato',
    [BACKEND_FONT_FAMILY_ENUM.POPPINS]: 'Poppins',
  };
  const result = mapping[backendValue];
  if (!result) {
    console.warn(`Unknown backend font family enum: ${backendValue}, falling back to System`);
    return 'System';
  }
  return result;
};

// Helper function to check if a value is a backend enum
const isBackendFontFamilyEnum = (value: string): value is BackendFontFamilyEnum => {
  return Object.values(BACKEND_FONT_FAMILY_ENUM).includes(value as BackendFontFamilyEnum);
};

export const useFontFamily = () => {
  const [currentWorkspaceMember, setCurrentWorkspaceMember] = useRecoilState(
    currentWorkspaceMemberState,
  );

  const { updateOneRecord: updateOneWorkspaceMember } = useUpdateOneRecord({
    objectNameSingular: CoreObjectNameSingular.WorkspaceMember,
  });

  // Convert backend value to frontend value when reading
  const fontFamily = currentWorkspaceMember?.fontFamily 
    ? (typeof currentWorkspaceMember.fontFamily === 'string' && isBackendFontFamilyEnum(currentWorkspaceMember.fontFamily)
        ? backendEnumToFontFamily(currentWorkspaceMember.fontFamily)
        : currentWorkspaceMember.fontFamily as FontFamily)
    : 'System';

  const setFontFamily = useCallback(
    async (value: FontFamily) => {
      if (!currentWorkspaceMember) {
        return;
      }
      
      const backendValue = fontFamilyToBackendEnum(value);
      
      // Update frontend state with frontend value
      setCurrentWorkspaceMember((current) => {
        if (!current) {
          return current;
        }
        return {
          ...current,
          fontFamily: value, // Keep frontend value in state
        };
      });
      
      // Send backend value to API
      await updateOneWorkspaceMember?.({
        idToUpdate: currentWorkspaceMember?.id,
        updateOneRecordInput: {
          fontFamily: backendValue,
        },
      });
    },
    [
      currentWorkspaceMember,
      setCurrentWorkspaceMember,
      updateOneWorkspaceMember,
    ],
  );

  const fontFamilyList: Array<{
    id: FontFamily;
    label: string;
  }> = [
    {
      id: 'System',
      label: 'System',
    },
    {
      id: 'Inter',
      label: 'Inter',
    },
    {
      id: 'Roboto',
      label: 'Roboto',
    },
    {
      id: 'Open Sans',
      label: 'Open Sans',
    },
    {
      id: 'Lato',
      label: 'Lato',
    },
    {
      id: 'Poppins',
      label: 'Poppins',
    },
  ];

  return {
    fontFamily,
    setFontFamily,
    fontFamilyList,
  };
};